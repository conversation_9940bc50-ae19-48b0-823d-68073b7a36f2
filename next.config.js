/** @type {import('next').NextConfig} */
const { version } = require("./package.json");

const nextConfig = {
  poweredByHeader: false, // Hide x-powered-by header
  images: {
    domains: [process.env.NEXT_PUBLIC_S3_DOMAIN],
    // Add image optimization settings to prevent preload issues
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  env: {
    VERSION: version,
  },
  // Optimize CSS loading to reduce preload warnings
  experimental: {
    optimizeCss: false, // Disable to prevent ReactDOM.preload empty href errors
  },
  async headers() {
    return [
      // Static assets - allow caching for performance
      {
        source: "/_next/static/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
          // Content Security Policy for static assets
          {
            key: "Content-Security-Policy",
            value: [
              "default-src 'self';",
              "script-src 'self' 'unsafe-inline' https://www.googletagmanager.com https://www.google-analytics.com https://cmp.osano.com https://*.osano.com https://www.gstatic.com https://www.google.com https://www.recaptcha.net https://snap.licdn.com https://www.linkedin.com https://*.ads.linkedin.com;",
              "worker-src 'self' blob:;",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;",
              "font-src 'self' https://fonts.gstatic.com data:;",
              "img-src 'self' data: blob: https:;",
              "connect-src 'self' https://www.google-analytics.com https://region1.google-analytics.com https://analytics.google.com https://stats.g.doubleclick.net https://www.googletagmanager.com https://www.google.com https://ipapi.co https://cmp.osano.com https://*.osano.com https://*.amazonaws.com https://*.amplifyapp.com https://*.apphero.io https://px.ads.linkedin.com https://www.linkedin.com https://*.linkedin.com;",
              "frame-src 'self' https://www.google.com https://www.recaptcha.net https://recaptcha.google.com https://www.googletagmanager.com https://td.doubleclick.net https://*.doubleclick.net https://*.apphero.io https://*.amplifyapp.com;",
              "object-src 'none';",
              "base-uri 'self';",
              "form-action 'self';",
              "frame-ancestors 'self';",
              "upgrade-insecure-requests;",
              "block-all-mixed-content;",
              "report-uri /api/csp-report;",
            ].join(" "),
          },
          // X-Content-Type-Options
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          // X-Frame-Options
          {
            key: "X-Frame-Options",
            value: "SAMEORIGIN",
          },
          // Referrer Policy
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
          // X-XSS-Protection
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
        ],
      },
      {
        source: "/favicon.ico",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=86400",
          },
        ],
      },
      {
        source: "/:path*\\.(svg|png|jpg|jpeg|gif|webp|ico|woff|woff2|ttf|eot)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=86400",
          },
          // Content Security Policy for font and image files
          {
            key: "Content-Security-Policy",
            value: [
              "default-src 'self';",
              "script-src 'self' 'unsafe-inline' https://www.googletagmanager.com https://www.google-analytics.com https://cmp.osano.com https://*.osano.com https://www.gstatic.com https://www.google.com https://www.recaptcha.net https://snap.licdn.com https://www.linkedin.com https://*.ads.linkedin.com;",
              "worker-src 'self' blob:;",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;",
              "font-src 'self' https://fonts.gstatic.com data:;",
              "img-src 'self' data: blob: https:;",
              "connect-src 'self' https://www.google-analytics.com https://cmp.osano.com https://*.osano.com https://www.gstatic.com https://www.google.com https://www.recaptcha.net https://snap.licdn.com https://www.linkedin.com https://*.ads.linkedin.com;",
              "frame-src 'self' https://www.google.com https://www.recaptcha.net https://snap.licdn.com https://www.linkedin.com;",
              "object-src 'none';",
              "base-uri 'self';",
              "form-action 'self';",
              "frame-ancestors 'self';",
              "upgrade-insecure-requests;",
              "block-all-mixed-content;",
              "report-uri /api/csp-report;",
            ].join(" "),
          },
          // X-Content-Type-Options
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          // X-Frame-Options
          {
            key: "X-Frame-Options",
            value: "SAMEORIGIN",
          },
          // Referrer Policy
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
          // X-XSS-Protection
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
        ],
      },
      // Service worker and manifest - prevent caching
      {
        source: "/sw.js",
        headers: [
          {
            key: "Cache-Control",
            value: "no-cache, no-store, must-revalidate, private, max-age=0",
          },
          {
            key: "Pragma",
            value: "no-cache",
          },
          {
            key: "Expires",
            value: "0",
          },
        ],
      },
      {
        source: "/manifest.json",
        headers: [
          {
            key: "Cache-Control",
            value: "no-cache, no-store, must-revalidate, private, max-age=0",
          },
          {
            key: "Pragma",
            value: "no-cache",
          },
          {
            key: "Expires",
            value: "0",
          },
        ],
      },
      {
        source: "/service-worker.js",
        headers: [
          {
            key: "Cache-Control",
            value: "no-cache, no-store, must-revalidate, private, max-age=0",
          },
          {
            key: "Pragma",
            value: "no-cache",
          },
          {
            key: "Expires",
            value: "0",
          },
        ],
      },
      // API routes - strict no-cache
      {
        source: "/api/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "no-cache, no-store, must-revalidate, private, max-age=0",
          },
          {
            key: "Pragma",
            value: "no-cache",
          },
          {
            key: "Expires",
            value: "0",
          },
          {
            key: "Vary",
            value: "Authorization, Accept-Encoding",
          },
        ],
      },
      // All other routes - prevent caching
      {
        source: "/((?!_next/static|favicon\\.ico).*)",
        headers: [
          // Content Security Policy - Environment-aware CSP
          {
            key: "Content-Security-Policy",
            value: (() => {
              return [
                "default-src 'self';",
                "script-src 'self' 'unsafe-inline' https://www.googletagmanager.com https://www.google-analytics.com https://cmp.osano.com https://*.osano.com https://www.gstatic.com https://www.google.com https://www.recaptcha.net https://snap.licdn.com https://www.linkedin.com https://*.ads.linkedin.com;",
                "worker-src 'self' blob:;",
                "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;",
                "font-src 'self' https://fonts.gstatic.com data:;",
                "img-src 'self' data: blob: https:;",
                "connect-src 'self' https://www.google-analytics.com https://region1.google-analytics.com https://analytics.google.com https://stats.g.doubleclick.net https://www.googletagmanager.com https://www.google.com https://ipapi.co https://cmp.osano.com https://*.osano.com https://*.amazonaws.com https://*.amplifyapp.com https://*.apphero.io https://px.ads.linkedin.com https://www.linkedin.com https://*.linkedin.com;",
                "frame-src 'self' https://www.google.com https://www.recaptcha.net https://recaptcha.google.com https://www.googletagmanager.com https://td.doubleclick.net https://*.doubleclick.net https://*.apphero.io https://*.amplifyapp.com;",
                "object-src 'none';",
                "base-uri 'self';",
                "form-action 'self';",
                "frame-ancestors 'self';",
                "upgrade-insecure-requests;",
                "block-all-mixed-content;",
                "report-uri /api/csp-report;",
              ].join(" ");
            })(),
          },
          // X-Content-Type-Options
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          // X-Frame-Options
          {
            key: "X-Frame-Options",
            value: "SAMEORIGIN",
          },
          // Referrer Policy
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
          // X-XSS-Protection
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          // Strict Transport Security
          {
            key: "Strict-Transport-Security",
            value: "max-age=31536000; includeSubDomains; preload",
          },
          // Permissions Policy - More comprehensive
          {
            key: "Permissions-Policy",
            value: [
              "geolocation=()",
              "microphone=()",
              "camera=()",
              "payment=()",
              "usb=()",
              "magnetometer=()",
              "gyroscope=()",
              "speaker=()",
              "vibrate=()",
              "fullscreen=(self)",
              "sync-xhr=()"
            ].join(", "),
          },
          // Cross-Origin-Embedder-Policy
          {
            key: "Cross-Origin-Embedder-Policy",
            value: "unsafe-none",
          },
          // Cross-Origin-Opener-Policy
          {
            key: "Cross-Origin-Opener-Policy",
            value: "same-origin-allow-popups",
          },
          // Cross-Origin-Resource-Policy
          {
            key: "Cross-Origin-Resource-Policy",
            value: "cross-origin",
          },
          // Require SRI for subresources
          {
            key: "Require-SRI-For",
            value: "script style",
          },
          // Cache Control - Prevent caching of sensitive content
          {
            key: "Cache-Control",
            value: "no-cache, no-store, must-revalidate, private, max-age=0",
          },
          // Pragma - HTTP/1.0 compatibility
          {
            key: "Pragma",
            value: "no-cache",
          },
          // Expires - Set expiration date in the past
          {
            key: "Expires",
            value: "0",
          },
          // Vary - Prevent caching based on headers
          {
            key: "Vary",
            value: "Authorization, Accept-Encoding, User-Agent",
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
